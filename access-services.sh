#!/bin/bash

# <PERSON>ript to access AKS ClusterIP services from Ubuntu jump server
# This script sets up port forwarding for various services

echo "Setting up access to AKS ClusterIP services..."

# Function to start port forwarding in background
start_port_forward() {
    local service_name=$1
    local local_port=$2
    local service_port=$3
    local namespace=${4:-default}
    
    echo "Starting port forward for $service_name on port $local_port..."
    kubectl port-forward --address 0.0.0.0 service/$service_name $local_port:$service_port -n $namespace &
    echo "Port forward PID: $!"
}

# Kill existing port forwards
echo "Cleaning up existing port forwards..."
pkill -f "kubectl port-forward"
sleep 2

# Start port forwarding for each service
echo "Starting port forwards..."

# Jenkins service (8080 -> 8080, 50000 -> 50000)
start_port_forward "jenkins" "8080" "8080" "default"
start_port_forward "jenkins" "50000" "50000" "default"

# Apache service (8081 -> 80)
start_port_forward "apache-service" "8081" "80" "default"

# Hello World service (8082 -> 80)
start_port_forward "hello-world" "8082" "80" "default"

echo ""
echo "Port forwarding setup complete!"
echo "Services are now accessible on:"
echo "  Jenkins:     http://localhost:8080 (UI) and localhost:50000 (agents)"
echo "  Apache:      http://localhost:8081"
echo "  Hello World: http://localhost:8082"
echo ""
echo "To stop all port forwards, run: pkill -f 'kubectl port-forward'"
echo "To check running port forwards, run: ps aux | grep 'kubectl port-forward'"
