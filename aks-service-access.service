[Unit]
Description=AKS Service Access via Port Forwarding
After=network.target
Wants=network.target

[Service]
Type=forking
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/test
Environment=HOME=/home/<USER>
Environment=KUBECONFIG=/home/<USER>/test/config
ExecStart=/home/<USER>/test/service-access-manager.sh start-forwards
ExecStop=/home/<USER>/test/service-access-manager.sh stop
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
