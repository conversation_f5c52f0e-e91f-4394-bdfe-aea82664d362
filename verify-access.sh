#!/bin/bash

# Script to verify access to AKS ClusterIP services from Ubuntu jump server

echo "Verifying access to AKS services..."
echo "=================================="

# Get the jump server IP
JUMP_IP=$(hostname -I | awk '{print $1}')
echo "Jump Server IP: $JUMP_IP"
echo ""

# Test each service
test_service() {
    local name=$1
    local port=$2
    local path=${3:-/}
    
    echo -n "Testing $name on port $port... "
    
    if curl -s --connect-timeout 5 "http://localhost:$port$path" > /dev/null; then
        echo "✓ SUCCESS"
        return 0
    else
        echo "✗ FAILED"
        return 1
    fi
}

# Test services
echo "Testing local access:"
test_service "Jenkins" "8080" "/login"
test_service "Apache" "8081" "/"
test_service "Hello World" "8082" "/"
echo ""

# Show running port forwards
echo "Active port forwards:"
ps aux | grep "kubectl port-forward" | grep -v grep | while read line; do
    echo "  $line"
done
echo ""

# Show service endpoints
echo "Service endpoints in cluster:"
kubectl get endpoints
echo ""

echo "Access URLs:"
echo "  Jenkins:     http://$JUMP_IP:8080"
echo "  Apache:      http://$JUMP_IP:8081" 
echo "  Hello World: http://$JUMP_IP:8082"
