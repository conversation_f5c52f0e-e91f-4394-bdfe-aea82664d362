#!/bin/bash

# Script to set up kubectl proxy for accessing AKS services
# This provides access to all services through the Kubernetes API proxy

echo "Setting up kubectl proxy for AKS cluster access..."

# Kill existing proxy
echo "Cleaning up existing kubectl proxy..."
pkill -f "kubectl proxy"
sleep 2

# Start kubectl proxy on all interfaces
echo "Starting kubectl proxy on port 8001..."
kubectl proxy --address='0.0.0.0' --port=8001 --accept-hosts='^.*' &
PROXY_PID=$!

echo "kubectl proxy started with PID: $PROXY_PID"
echo ""
echo "Services are now accessible via proxy URLs:"
echo "  Jenkins:     http://localhost:8001/api/v1/namespaces/default/services/jenkins:8080/proxy/"
echo "  Apache:      http://localhost:8001/api/v1/namespaces/default/services/apache-service:80/proxy/"
echo "  Hello World: http://localhost:8001/api/v1/namespaces/default/services/hello-world:80/proxy/"
echo ""
echo "Kubernetes Dashboard (if installed):"
echo "  Dashboard:   http://localhost:8001/api/v1/namespaces/kubernetes-dashboard/services/https:kubernetes-dashboard:/proxy/"
echo ""
echo "To stop the proxy, run: kill $PROXY_PID"
echo "Or use: pkill -f 'kubectl proxy'"
