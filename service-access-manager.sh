#!/bin/bash

# Comprehensive service access manager for AKS ClusterIP services
# Provides multiple methods to access services from Ubuntu jump server

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/service-access.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_colored() {
    echo -e "${2}${1}${NC}"
}

show_services() {
    print_colored "Current AKS Services:" "$BLUE"
    kubectl get services -o wide
    echo ""
}

start_port_forwards() {
    print_colored "Starting port forwarding for all services..." "$GREEN"
    
    # Kill existing port forwards
    pkill -f "kubectl port-forward" 2>/dev/null
    sleep 2
    
    # Jenkins
    kubectl port-forward --address 0.0.0.0 service/jenkins 8080:8080 &
    kubectl port-forward --address 0.0.0.0 service/jenkins 50000:50000 &
    
    # Apache
    kubectl port-forward --address 0.0.0.0 service/apache-service 8081:80 &
    
    # Hello World
    kubectl port-forward --address 0.0.0.0 service/hello-world 8082:80 &
    
    sleep 3
    log "Port forwarding started for all services"
    show_access_urls
}

start_proxy() {
    print_colored "Starting kubectl proxy..." "$GREEN"
    
    # Kill existing proxy
    pkill -f "kubectl proxy" 2>/dev/null
    sleep 2
    
    # Start proxy
    kubectl proxy --address='0.0.0.0' --port=8001 --accept-hosts='^.*' &
    sleep 3
    
    log "kubectl proxy started on port 8001"
    show_proxy_urls
}

show_access_urls() {
    print_colored "Direct Access URLs (Port Forward):" "$YELLOW"
    echo "  Jenkins UI:    http://$(hostname -I | awk '{print $1}'):8080"
    echo "  Jenkins Agent: http://$(hostname -I | awk '{print $1}'):50000"
    echo "  Apache:        http://$(hostname -I | awk '{print $1}'):8081"
    echo "  Hello World:   http://$(hostname -I | awk '{print $1}'):8082"
    echo ""
}

show_proxy_urls() {
    local ip=$(hostname -I | awk '{print $1}')
    print_colored "Proxy Access URLs:" "$YELLOW"
    echo "  Jenkins:     http://$ip:8001/api/v1/namespaces/default/services/jenkins:8080/proxy/"
    echo "  Apache:      http://$ip:8001/api/v1/namespaces/default/services/apache-service:80/proxy/"
    echo "  Hello World: http://$ip:8001/api/v1/namespaces/default/services/hello-world:80/proxy/"
    echo ""
}

stop_all() {
    print_colored "Stopping all port forwards and proxies..." "$RED"
    pkill -f "kubectl port-forward" 2>/dev/null
    pkill -f "kubectl proxy" 2>/dev/null
    log "All services stopped"
}

show_status() {
    print_colored "Service Access Status:" "$BLUE"
    
    echo "Port Forwards:"
    ps aux | grep "kubectl port-forward" | grep -v grep || echo "  No port forwards running"
    
    echo ""
    echo "Kubectl Proxy:"
    ps aux | grep "kubectl proxy" | grep -v grep || echo "  No proxy running"
    
    echo ""
    show_services
}

test_connectivity() {
    print_colored "Testing service connectivity..." "$BLUE"
    
    local services=("jenkins:8080" "apache-service:80" "hello-world:80")
    
    for service in "${services[@]}"; do
        local name=$(echo $service | cut -d: -f1)
        local port=$(echo $service | cut -d: -f2)
        
        echo -n "Testing $name... "
        if kubectl exec -it $(kubectl get pods -l app=$name -o jsonpath='{.items[0].metadata.name}') -- wget -q --spider --timeout=5 localhost:$port 2>/dev/null; then
            print_colored "✓ OK" "$GREEN"
        else
            print_colored "✗ Failed" "$RED"
        fi
    done
}

show_help() {
    print_colored "AKS Service Access Manager" "$BLUE"
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start-forwards  Start port forwarding for all services"
    echo "  start-proxy     Start kubectl proxy"
    echo "  stop           Stop all port forwards and proxies"
    echo "  status         Show current status"
    echo "  services       Show available services"
    echo "  test           Test service connectivity"
    echo "  urls           Show access URLs"
    echo "  help           Show this help message"
    echo ""
}

# Main script logic
case "${1:-help}" in
    "start-forwards"|"forwards")
        start_port_forwards
        ;;
    "start-proxy"|"proxy")
        start_proxy
        ;;
    "stop")
        stop_all
        ;;
    "status")
        show_status
        ;;
    "services")
        show_services
        ;;
    "test")
        test_connectivity
        ;;
    "urls")
        show_access_urls
        show_proxy_urls
        ;;
    "help"|*)
        show_help
        ;;
esac
