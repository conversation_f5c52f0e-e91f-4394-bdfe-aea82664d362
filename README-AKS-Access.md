# AKS ClusterIP Service Access from Ubuntu Jump Server

This setup enables the Ubuntu jump server to access AKS services that are configured with ClusterIP (internal-only access).

## Current Services Available

- **<PERSON>**: ClusterIP `***********` - Ports 8080 (UI), 50000 (agents)
- **Apache**: ClusterIP `**********` - Port 80
- **Hello World**: ClusterIP `***********` - Port 80

## Access Methods

### Method 1: Service Access Manager (Recommended)

Use the comprehensive service manager script:

```bash
# Start port forwarding for all services
./service-access-manager.sh start-forwards

# Start kubectl proxy instead
./service-access-manager.sh start-proxy

# Check status
./service-access-manager.sh status

# Stop all services
./service-access-manager.sh stop

# Show available commands
./service-access-manager.sh help
```

### Method 2: Individual Scripts

```bash
# Simple port forwarding
./access-services.sh

# kubectl proxy setup
./setup-kubectl-proxy.sh
```

## Access URLs

### Direct Access (Port Forward Method)
- **<PERSON> UI**: http://**********:8080
- **<PERSON> Agents**: http://**********:50000
- **Apache**: http://**********:8081
- **Hello World**: http://**********:8082

### Proxy Access (kubectl proxy method)
- **Jenkins**: http://**********:8001/api/v1/namespaces/default/services/jenkins:8080/proxy/
- **Apache**: http://**********:8001/api/v1/namespaces/default/services/apache-service:80/proxy/
- **Hello World**: http://**********:8001/api/v1/namespaces/default/services/hello-world:80/proxy/

## Verification

Run the verification script to test connectivity:

```bash
./verify-access.sh
```

## Persistent Access (Optional)

To make the service access persistent across reboots:

```bash
# Copy the systemd service file
sudo cp aks-service-access.service /etc/systemd/system/

# Enable and start the service
sudo systemctl enable aks-service-access.service
sudo systemctl start aks-service-access.service

# Check status
sudo systemctl status aks-service-access.service
```

## Troubleshooting

### Check Running Port Forwards
```bash
ps aux | grep "kubectl port-forward"
```

### Check kubectl Proxy
```bash
ps aux | grep "kubectl proxy"
```

### Stop All Services
```bash
pkill -f "kubectl port-forward"
pkill -f "kubectl proxy"
```

### Check Service Status in Cluster
```bash
kubectl get services
kubectl get pods
kubectl get endpoints
```

## Security Notes

- Port forwarding binds to all interfaces (0.0.0.0) for external access
- Consider firewall rules if accessing from other networks
- kubectl proxy accepts all hosts - use with caution in production
- Services are accessible from any machine that can reach the jump server

## Files Created

- `service-access-manager.sh` - Main service management script
- `access-services.sh` - Simple port forwarding script
- `setup-kubectl-proxy.sh` - kubectl proxy setup script
- `verify-access.sh` - Connectivity verification script
- `aks-service-access.service` - Systemd service file
- `service-access.log` - Log file for service operations
